@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}

@layer base {
  :root {
    --background: 0 0% 94%; /* Light gray #F0F0F0 */
    --foreground: 0 0% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 3.9%;
    --primary: 231 48% 48%; /* Deep blue #3F51B5 */
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96.1%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96.1%; /* Default muted is fine, can be used for board squares */
    --muted-foreground: 0 0% 45.1%;
    --accent: 45 100% 51%; /* Amber #FFC107 */
    --accent-foreground: 0 0% 3.9%; /* Dark text for contrast with Amber */
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 89.8%;
    --input: 0 0% 89.8%;
    --ring: 231 48% 48%; /* Ring color to match primary */
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;

    /* Sidebar specific colors - adjust if sidebar is used, or keep defaults */
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 231 48% 48%; /* Match app primary */
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 45 100% 51%; /* Match app accent */
    --sidebar-accent-foreground: 0 0% 3.9%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 231 48% 48%; /* Match app primary for ring */
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 231 48% 65%; /* Lighter blue for dark mode */
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 45 100% 51%; /* Amber can stay same or be adjusted */
    --accent-foreground: 0 0% 3.9%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 231 48% 65%; /* Lighter blue for dark mode ring */
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;

    /* Dark Sidebar specific colors */
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 231 48% 65%; /* Match app primary (dark) */
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 45 100% 51%; /* Match app accent (dark) */
    --sidebar-accent-foreground: 0 0% 3.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 231 48% 65%; /* Match app primary for ring (dark) */
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
