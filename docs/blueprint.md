# **App Name**: Queen's Gambit Visualizer

## Core Features:

- Chessboard Display: Display the solved chessboard with the queens placed on it.
- Interactive Placement: Allow the user to manually move the queens on the board.
- Board Controls: Provide options to solve another random configuration or to clear the board.

## Style Guidelines:

- Primary color: Deep blue (#3F51B5), evoking intelligence and strategy related to the game.
- Background color: Light gray (#F0F0F0), providing a neutral backdrop to highlight the chessboard and queens.
- Accent color: Amber (#FFC107), used for interactive elements and highlighting queen positions, to add contrast and indicate interactivity.
- Font: 'Inter' (sans-serif) for both headings and body text, to ensure legibility and maintain a clean, modern look.
- Use simple, geometric icons to represent control elements, ensuring clear visual communication and ease of use.
- A centered chessboard layout that maximizes focus on the puzzle; surrounding controls placed intuitively for interaction.
- Subtle animations when the AI solver places queens, or when users drag and drop them, to confirm placement actions and add engagement.